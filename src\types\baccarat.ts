
// Define card suits
export enum Suit {
  Hearts = "hearts",
  Diamonds = "diamonds", 
  Clubs = "clubs",
  Spades = "spades"
}

// Define card values
export enum Value {
  Ace = "A",
  Two = "2",
  Three = "3",
  Four = "4",
  Five = "5",
  Six = "6",
  Seven = "7",
  Eight = "8",
  Nine = "9",
  Ten = "10",
  <PERSON> = "J",
  <PERSON> = "Q",
  <PERSON> = "K"
}

// Card interface
export interface Card {
  suit: Suit;
  value: Value;
  isRevealed: boolean;
}

// Bet types
export enum BetType {
  Banker = "banker",
  Player = "player",
  Tie = "tie",
  BankerPair = "banker-pair",
  PlayerPair = "player-pair",
  Lucky6 = "lucky-6",
  Lucky7 = "lucky-7"
}

// Bet interface
export interface Bet {
  type: BetType;
  amount: number;
}

// Game result
export enum GameResult {
  BankerWin = "banker-win",
  PlayerWin = "player-win",
  Tie = "tie"
}

// Game history entry for road maps
export interface GameHistoryEntry {
  result: GameResult;
  bankerCards: Card[];
  playerCards: Card[];
  bankerScore: number;
  playerScore: number;
  bankerPair: boolean;
  playerPair: boolean;
  lucky6: boolean;
  lucky7: boolean;
}

// Game state
export enum GameState {
  Betting = "betting",
  Dealing = "dealing",
  Revealing = "revealing",
  Result = "result",
}

// Player interface
export interface Player {
  balance: number;
  bets: Bet[];
}

// Payout rates for different bet types
export const PayoutRates = {
  [BetType.Banker]: { rate: 0.95, description: "1:0.95" }, // 5% commission
  [BetType.Player]: { rate: 1, description: "1:1" },
  [BetType.Tie]: { rate: 8, description: "1:8" },
  [BetType.BankerPair]: { rate: 11, description: "1:11" },
  [BetType.PlayerPair]: { rate: 11, description: "1:11" },
  [BetType.Lucky6]: { 
    twoCards: { rate: 12, description: "1:12" },
    threeCards: { rate: 20, description: "1:20" }
  },
  [BetType.Lucky7]: {
    fourCards: { rate: 6, description: "1:6" },
    fiveCards: { rate: 15, description: "1:15" },
    superFourCards: { rate: 30, description: "1:30" },
    superFiveCards: { rate: 40, description: "1:40" },
    superSixCards: { rate: 100, description: "1:100" }
  }
};
