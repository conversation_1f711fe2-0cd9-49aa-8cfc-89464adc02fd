
import { useState, useEffect, useCallback } from "react";
import Card from "./Card";
import Chip from "./Chip";
import BettingArea from "./BettingArea";
import CountdownTimer from "./CountdownTimer";
import RoadMaps from "./RoadMaps";
import ResultModal from "./ResultModal";
import HelpModal from "./HelpModal";
import { 
  BetType, 
  GameState, 
  GameResult,
  Bet, 
  Player, 
  GameHistoryEntry,
} from "../types/baccarat";
import {
  initializeGame,
  dealInitialCards,
  handleThirdCard,
  determineResult,
  createGameHistoryEntry,
  calculatePayout
} from "../utils/gameLogic";
import { calculateHandValue } from "../utils/cardUtils";
import { toast } from "@/components/ui/use-toast";

const COUNTDOWN_SECONDS = 15;
const CHIPS = [
  { value: 5, color: "bg-gray-500 border-gray-600" },
  { value: 25, color: "bg-red-500 border-red-600" },
  { value: 50, color: "bg-blue-500 border-blue-600" },
  { value: 100, color: "bg-green-500 border-green-600" },
  { value: 500, color: "bg-purple-500 border-purple-600" },
  { value: 1000, color: "bg-yellow-500 border-yellow-600" }
];

const INITIAL_BALANCE = 10000;

const BaccaratGame: React.FC = () => {
  // Game state
  const [gameState, setGameState] = useState<GameState>(GameState.Betting);
  const [gameHistory, setGameHistory] = useState<GameHistoryEntry[]>([]);
  
  // Game data
  const [gameData, setGameData] = useState(() => initializeGame());
  const [playerCards, setPlayerCards] = useState<any[]>([]);
  const [bankerCards, setBankerCards] = useState<any[]>([]);
  
  // Player data
  const [player, setPlayer] = useState<Player>({
    balance: INITIAL_BALANCE,
    bets: []
  });
  
  // Betting state
  const [selectedChip, setSelectedChip] = useState<number>(CHIPS[0].value);
  const [currentBets, setCurrentBets] = useState<Record<BetType, number>>({
    [BetType.Banker]: 0,
    [BetType.Player]: 0,
    [BetType.Tie]: 0,
    [BetType.BankerPair]: 0,
    [BetType.PlayerPair]: 0,
    [BetType.Lucky6]: 0,
    [BetType.Lucky7]: 0
  });
  
  // Results and display
  const [gameResult, setGameResult] = useState<GameResult | null>(null);
  const [bankerScore, setBankerScore] = useState(0);
  const [playerScore, setPlayerScore] = useState(0);
  const [winAmount, setWinAmount] = useState(0);
  const [showResultModal, setShowResultModal] = useState(false);
  const [showHelpModal, setShowHelpModal] = useState(false);
  
  // Handle chip selection
  const handleChipSelect = (value: number) => {
    setSelectedChip(value);
  };
  
  // Handle bet placement
  const handleBetPlaced = (betType: BetType) => {
    if (gameState !== GameState.Betting) return;
    
    const chipValue = selectedChip;
    const totalBet = Object.values(currentBets).reduce((sum, bet) => sum + bet, 0);
    
    // Check if player has enough balance
    if (player.balance - totalBet - chipValue < 0) {
      toast({
        title: "余额不足",
        description: "您没有足够的余额来下注该金额",
        variant: "destructive"
      });
      return;
    }
    
    setCurrentBets(prev => ({
      ...prev,
      [betType]: prev[betType] + chipValue
    }));
  };
  
  // Start game when countdown completes
  const handleCountdownComplete = useCallback(() => {
    // Convert current bets to bet array
    const bets: Bet[] = [];
    Object.entries(currentBets).forEach(([type, amount]) => {
      if (amount > 0) {
        bets.push({
          type: type as BetType,
          amount
        });
      }
    });
    
    // Update player's bets and deduct balance
    if (bets.length > 0) {
      const totalBet = bets.reduce((sum, bet) => sum + bet.amount, 0);
      setPlayer(prev => ({
        ...prev,
        balance: prev.balance - totalBet,
        bets
      }));
    } else {
      toast({
        title: "无投注",
        description: "请至少下一个注",
        variant: "destructive"
      });
      return;
    }
    
    // Start dealing cards
    setGameState(GameState.Dealing);
  }, [currentBets]);
  
  // Deal cards animation
  useEffect(() => {
    if (gameState !== GameState.Dealing) return;
    
    const timer = setTimeout(() => {
      const { playerCards, bankerCards, nextIndex } = dealInitialCards(
        gameData.cards,
        gameData.currentIndex
      );
      
      setPlayerCards(playerCards);
      setBankerCards(bankerCards);
      
      // Update game data
      setGameData(prev => ({
        ...prev,
        currentIndex: nextIndex
      }));
      
      // Move to revealing cards
      setGameState(GameState.Revealing);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [gameState, gameData]);
  
  // Handle third card drawing and result determination
  useEffect(() => {
    if (gameState !== GameState.Revealing) return;
    
    const timer = setTimeout(() => {
      // Calculate initial scores
      const initialPlayerScore = calculateHandValue(playerCards);
      const initialBankerScore = calculateHandValue(bankerCards);
      
      setPlayerScore(initialPlayerScore);
      setBankerScore(initialBankerScore);
      
      // Check if we need to draw a third card
      const { playerCards: updatedPlayerCards, bankerCards: updatedBankerCards, nextIndex } = handleThirdCard(
        playerCards,
        bankerCards,
        gameData.cards,
        gameData.currentIndex
      );
      
      setPlayerCards(updatedPlayerCards);
      setBankerCards(updatedBankerCards);
      
      // Update game data
      setGameData(prev => ({
        ...prev,
        currentIndex: nextIndex
      }));
      
      // Calculate final scores
      const finalPlayerScore = calculateHandValue(updatedPlayerCards);
      const finalBankerScore = calculateHandValue(updatedBankerCards);
      
      setPlayerScore(finalPlayerScore);
      setBankerScore(finalBankerScore);
      
      // Determine result
      const result = determineResult(updatedPlayerCards, updatedBankerCards);
      setGameResult(result);
      
      // Create game history entry
      const historyEntry = createGameHistoryEntry(updatedPlayerCards, updatedBankerCards, result);
      
      // Calculate payout
      const payout = calculatePayout(player, historyEntry);
      setWinAmount(payout);
      
      // Update player's balance with winnings
      if (payout > 0) {
        setPlayer(prev => ({
          ...prev,
          balance: prev.balance + payout
        }));
      }
      
      // Update game history
      setGameHistory(prev => [...prev, historyEntry]);
      
      // Show result modal
      setShowResultModal(true);
      
      // Move to result state
      setGameState(GameState.Result);
      
      // If deck is running low, reshuffle
      if (gameData.currentIndex > gameData.cards.length - 20) {
        setGameData(initializeGame());
        toast({
          title: "重新洗牌",
          description: "牌堆已重新洗牌"
        });
      }
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [gameState, playerCards, bankerCards, player, gameData]);
  
  // Start new round
  const startNewRound = () => {
    // Reset game state
    setGameState(GameState.Betting);
    setPlayerCards([]);
    setBankerCards([]);
    setGameResult(null);
    setPlayerScore(0);
    setBankerScore(0);
    setWinAmount(0);
    setShowResultModal(false);
    
    // Reset bets
    setCurrentBets({
      [BetType.Banker]: 0,
      [BetType.Player]: 0,
      [BetType.Tie]: 0,
      [BetType.BankerPair]: 0,
      [BetType.PlayerPair]: 0,
      [BetType.Lucky6]: 0,
      [BetType.Lucky7]: 0
    });
    
    // Reset player's bets
    setPlayer(prev => ({
      ...prev,
      bets: []
    }));
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black p-4 relative flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="text-white">
          <h1 className="text-3xl font-bold text-baccarat-gold">百家乐 Baccarat</h1>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="bg-baccarat-panel px-4 py-2 rounded-md">
            <div className="text-xs text-white/70">余额 (Balance)</div>
            <div className="text-xl font-bold text-baccarat-gold">${player.balance}</div>
          </div>
          
          <button 
            className="bg-baccarat-panel hover:bg-baccarat-panel/70 text-white px-4 py-2 rounded-md"
            onClick={() => setShowHelpModal(true)}
          >
            规则 (Rules)
          </button>
        </div>
      </div>
      
      {/* Game table */}
      <div className="baccarat-table rounded-3xl p-6 flex-grow mb-4">
        {/* Game area */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Player area */}
          <div className="bg-black/20 rounded-lg p-4">
            <div className="text-center mb-4">
              <h2 className="text-xl font-bold text-baccarat-blue">闲家 (Player)</h2>
              {playerScore > 0 && (
                <div className="text-3xl font-bold text-white">{playerScore}</div>
              )}
            </div>
            
            <div className="flex justify-center gap-4">
              {playerCards.map((card, index) => (
                <Card
                  key={`player-${index}`}
                  card={card}
                  isDealing={gameState === GameState.Dealing}
                  dealDelay={index * 300}
                />
              ))}
              {playerCards.length < 2 && (
                <div className="card opacity-0" />
              )}
              {playerCards.length < 3 && (
                <div className="card opacity-0" />
              )}
            </div>
          </div>
          
          {/* Center area with countdown */}
          <div className="flex flex-col items-center justify-center">
            <CountdownTimer
              seconds={COUNTDOWN_SECONDS}
              onComplete={handleCountdownComplete}
              isActive={gameState === GameState.Betting}
            />
            
            <div className="bg-baccarat-panel rounded-lg p-4 mt-4">
              <div className="text-center text-white mb-2">
                游戏状态 (Game State)
              </div>
              <div className="text-xl font-bold text-center text-baccarat-gold">
                {gameState === GameState.Betting && "下注中 (Betting)"}
                {gameState === GameState.Dealing && "发牌中 (Dealing)"}
                {gameState === GameState.Revealing && "揭牌中 (Revealing)"}
                {gameState === GameState.Result && "结算中 (Result)"}
              </div>
            </div>
            
            {gameState === GameState.Result && (
              <button
                className="mt-4 bg-baccarat-gold text-white py-2 px-6 rounded-md font-bold hover:bg-opacity-80"
                onClick={startNewRound}
              >
                新一局 (New Round)
              </button>
            )}
          </div>
          
          {/* Banker area */}
          <div className="bg-black/20 rounded-lg p-4">
            <div className="text-center mb-4">
              <h2 className="text-xl font-bold text-baccarat-red">庄家 (Banker)</h2>
              {bankerScore > 0 && (
                <div className="text-3xl font-bold text-white">{bankerScore}</div>
              )}
            </div>
            
            <div className="flex justify-center gap-4">
              {bankerCards.map((card, index) => (
                <Card
                  key={`banker-${index}`}
                  card={card}
                  isDealing={gameState === GameState.Dealing}
                  dealDelay={(index + 2) * 300}
                />
              ))}
              {bankerCards.length < 2 && (
                <div className="card opacity-0" />
              )}
              {bankerCards.length < 3 && (
                <div className="card opacity-0" />
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Betting controls */}
      <div className="grid grid-cols-1 md:grid-cols-12 gap-4 mb-4">
        {/* Chip selection */}
        <div className="md:col-span-2 bg-baccarat-panel p-4 rounded-lg">
          <h3 className="text-white text-center mb-3">筹码 (Chips)</h3>
          <div className="grid grid-cols-3 gap-2">
            {CHIPS.map(chip => (
              <Chip
                key={chip.value}
                value={chip.value}
                color={chip.color}
                isSelected={selectedChip === chip.value}
                onClick={handleChipSelect}
              />
            ))}
          </div>
        </div>
        
        {/* Betting areas */}
        <div className="md:col-span-10 bg-baccarat-panel p-4 rounded-lg grid grid-cols-2 md:grid-cols-7 gap-4">
          <BettingArea
            type={BetType.Player}
            currentBet={currentBets[BetType.Player]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
          
          <BettingArea
            type={BetType.Banker}
            currentBet={currentBets[BetType.Banker]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
          
          <BettingArea
            type={BetType.Tie}
            currentBet={currentBets[BetType.Tie]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
          
          <BettingArea
            type={BetType.PlayerPair}
            currentBet={currentBets[BetType.PlayerPair]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
          
          <BettingArea
            type={BetType.BankerPair}
            currentBet={currentBets[BetType.BankerPair]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
          
          <BettingArea
            type={BetType.Lucky6}
            currentBet={currentBets[BetType.Lucky6]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
          
          <BettingArea
            type={BetType.Lucky7}
            currentBet={currentBets[BetType.Lucky7]}
            onBetPlaced={handleBetPlaced}
            disabled={gameState !== GameState.Betting}
          />
        </div>
      </div>

      {/* Road maps moved to bottom */}
      <div className="bg-baccarat-panel p-4 rounded-lg mb-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">路单图 Road Maps</h2>
          <div className="text-white text-sm">
            总局数: {gameHistory.length}
          </div>
        </div>
        <RoadMaps history={gameHistory} />
      </div>
      
      {/* Modals */}
      {showResultModal && (
        <ResultModal
          result={gameResult}
          bankerScore={bankerScore}
          playerScore={playerScore}
          winAmount={winAmount}
          onClose={() => setShowResultModal(false)}
        />
      )}
      
      <HelpModal
        isOpen={showHelpModal}
        onClose={() => setShowHelpModal(false)}
      />
    </div>
  );
};

export default BaccaratGame;
