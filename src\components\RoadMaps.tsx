
import { GameH<PERSON>oryEntry, GameResult } from "../types/baccarat";
import { 
  generatePearlRoad, 
  generateBigRoad,
  generateBigEyeRoad,
  generateSmallRoad,
  generateCockroachRoad,
  calculateStatistics
} from "../utils/roadmapUtils";

interface RoadMapsProps {
  history: GameHistoryEntry[];
}

const RoadMaps: React.FC<RoadMapsProps> = ({ history }) => {
  const pearlRoad = generatePearlRoad(history);
  const bigRoad = generateBigRoad(history);
  const bigEyeRoad = generateBigEyeRoad(bigRoad);
  const smallRoad = generateSmallRoad(bigRoad);
  const cockroachRoad = generateCockroachRoad(bigRoad);
  const statistics = calculateStatistics(history);
  
  return (
    <div className="grid grid-cols-12 gap-4">
      {/* Pearl Road (珠盘路) */}
      <div className="col-span-3 bg-black/30 rounded-lg p-3">
        <h3 className="text-white text-sm mb-2">珠盘路</h3>
        <div className="grid grid-cols-12 gap-0.5">
          {pearlRoad.map((column, colIndex) => (
            column.map((result, rowIndex) => (
              <div 
                key={`pearl-${colIndex}-${rowIndex}`} 
                className="aspect-square"
              >
                {result === GameResult.BankerWin && (
                  <div className="w-full h-full banker-win" />
                )}
                {result === GameResult.PlayerWin && (
                  <div className="w-full h-full player-win" />
                )}
                {result === GameResult.Tie && (
                  <div className="w-full h-full tie-marker" />
                )}
              </div>
            ))
          ))}
        </div>
      </div>
      
      {/* Mid section with Big Road and Derived Roads */}
      <div className="col-span-7">
        {/* Big Road (大路) */}
        <div className="bg-black/30 rounded-lg p-3 mb-3">
          <h3 className="text-white text-sm mb-2">大路</h3>
          <div className="grid grid-cols-24 gap-0.5">
            {bigRoad.slice(1).map((column, colIndex) => (
              column.map((cell, rowIndex) => (
                cell && (
                  <div 
                    key={`big-${colIndex}-${rowIndex}`} 
                    className="aspect-square relative"
                  >
                    <div className={`w-full h-full ${
                      cell.result === GameResult.BankerWin ? 'banker-win' : 'player-win'
                    } flex items-center justify-center`}>
                      {cell.ties > 0 && (
                        <span className="text-[10px] font-bold text-white">
                          {cell.ties}
                        </span>
                      )}
                    </div>
                  </div>
                )
              ))
            ))}
          </div>
        </div>
        
        {/* Three derived roads */}
        <div className="grid grid-cols-3 gap-3">
          {/* Big Eye Boy (大眼仔) */}
          <div className="bg-black/30 rounded-lg p-3">
            <h3 className="text-white text-sm mb-2">大眼仔路</h3>
            <div className="grid grid-cols-6 gap-0.5">
              {bigEyeRoad.map((column, colIndex) => (
                column.map((dot, rowIndex) => (
                  <div 
                    key={`bigeye-${colIndex}-${rowIndex}`} 
                    className="aspect-square flex items-center justify-center"
                  >
                    {dot && (
                      <div className={`w-2/3 h-2/3 rounded-full ${
                        dot === 'red' ? 'bg-baccarat-red' : 'bg-baccarat-blue'
                      }`} />
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>
          
          {/* Small Road (小路) */}
          <div className="bg-black/30 rounded-lg p-3">
            <h3 className="text-white text-sm mb-2">小路</h3>
            <div className="grid grid-cols-6 gap-0.5">
              {smallRoad.map((column, colIndex) => (
                column.map((dot, rowIndex) => (
                  <div 
                    key={`small-${colIndex}-${rowIndex}`} 
                    className="aspect-square flex items-center justify-center"
                  >
                    {dot && (
                      <div className={`w-2/3 h-2/3 rounded-full ${
                        dot === 'red' ? 'bg-baccarat-red' : 'bg-baccarat-blue'
                      }`} />
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>
          
          {/* Cockroach Road (曱甴路) */}
          <div className="bg-black/30 rounded-lg p-3">
            <h3 className="text-white text-sm mb-2">曱甴路</h3>
            <div className="grid grid-cols-6 gap-0.5">
              {cockroachRoad.map((column, colIndex) => (
                column.map((symbol, rowIndex) => (
                  <div 
                    key={`cockroach-${colIndex}-${rowIndex}`} 
                    className="aspect-square flex items-center justify-center"
                  >
                    {symbol && (
                      <span className="text-xs text-white">
                        {symbol === 'right' ? '→' : '↘'}
                      </span>
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Statistics */}
      <div className="col-span-2 bg-black/30 rounded-lg p-3">
        <h3 className="text-white text-sm mb-2">统计数据</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-baccarat-red">庄:</span>
            <span className="text-white">{statistics.bankerWins}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-baccarat-blue">闲:</span>
            <span className="text-white">{statistics.playerWins}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-baccarat-tie">和:</span>
            <span className="text-white">{statistics.ties}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white">庄对:</span>
            <span className="text-white">{statistics.bankerPairs}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white">闲对:</span>
            <span className="text-white">{statistics.playerPairs}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white">幸运6:</span>
            <span className="text-white">{statistics.lucky6}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-white">幸运7:</span>
            <span className="text-white">{statistics.lucky7}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoadMaps;
