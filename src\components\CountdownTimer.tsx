
import { useEffect, useState } from "react";

interface CountdownTimerProps {
  seconds: number;
  onComplete: () => void;
  isActive: boolean;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ 
  seconds, 
  onComplete, 
  isActive 
}) => {
  const [timeLeft, setTimeLeft] = useState(seconds);
  
  useEffect(() => {
    if (!isActive) return;
    
    if (timeLeft <= 0) {
      onComplete();
      return;
    }
    
    const interval = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);
    
    return () => clearInterval(interval);
  }, [timeLeft, isActive, onComplete]);
  
  useEffect(() => {
    if (isActive) {
      setTimeLeft(seconds);
    }
  }, [isActive, seconds]);
  
  return (
    <div className="countdown-timer flex items-center justify-center">
      <span className={timeLeft <= 5 ? "text-red-500" : ""}>
        {timeLeft}
      </span>
    </div>
  );
};

export default CountdownTimer;
