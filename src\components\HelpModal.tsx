
import { BetType, PayoutRates } from "../types/baccarat";

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const HelpModal: React.FC<HelpModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-70 z-50">
      <div className="bg-baccarat-panel border-4 border-baccarat-gold rounded-lg p-6 w-3/4 max-w-3xl max-h-[80vh] overflow-y-auto animate-scale-in">
        <h2 className="text-2xl font-bold mb-4 text-baccarat-gold">百家乐规则 (Baccarat Rules)</h2>
        
        <div className="text-white mb-6">
          <h3 className="text-xl mb-2">游戏目标</h3>
          <p className="mb-4">
            百家乐的目标是猜测庄家（<PERSON><PERSON>）和闲家（Player）谁的牌点数总和会更接近9点。
          </p>
          
          <h3 className="text-xl mb-2">牌点计算</h3>
          <ul className="list-disc list-inside mb-4">
            <li>A = 1点</li>
            <li>2-9 = 面值</li>
            <li>10, J, Q, K = 0点</li>
            <li>如果点数总和超过9，则只取个位数（例如：7+8=15，实际算5点）</li>
          </ul>
          
          <h3 className="text-xl mb-2">游戏流程</h3>
          <ol className="list-decimal list-inside mb-4">
            <li>玩家在倒计时内下注</li>
            <li>庄家和闲家各发两张牌</li>
            <li>根据规则决定是否需要补第三张牌</li>
            <li>根据最终点数比较判定胜负</li>
          </ol>
          
          <h3 className="text-xl mb-2">补牌规则</h3>
          <p className="mb-2"><strong>闲家补牌规则：</strong></p>
          <ul className="list-disc list-inside mb-4">
            <li>如果前两张牌总点数为0-5点，则必须补牌</li>
            <li>如果前两张牌总点数为6-9点，则不补牌</li>
          </ul>
          
          <p className="mb-2"><strong>庄家补牌规则（取决于闲家是否补牌）：</strong></p>
          <ul className="list-disc list-inside mb-4">
            <li>如果闲家不补牌：
              <ul className="list-disc list-inside ml-4">
                <li>庄家点数为0-5点，则补牌</li>
                <li>庄家点数为6-9点，则不补牌</li>
              </ul>
            </li>
            <li>如果闲家补牌，庄家根据自己点数和闲家补的牌点数决定：
              <ul className="list-disc list-inside ml-4">
                <li>庄家点数为0-2点，无论闲家补什么牌，庄家都补牌</li>
                <li>庄家点数为3点，如果闲家补的不是8点，庄家补牌</li>
                <li>庄家点数为4点，如果闲家补的是2-7点，庄家补牌</li>
                <li>庄家点数为5点，如果闲家补的是4-7点，庄家补牌</li>
                <li>庄家点数为6点，如果闲家补的是6-7点，庄家补牌</li>
                <li>庄家点数为7-9点，庄家不补牌</li>
              </ul>
            </li>
          </ul>
          
          <h3 className="text-xl mb-2">投注选项和赔率</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-black/20 p-3 rounded-md">
              <h4 className="text-lg font-bold mb-2">庄家 (Banker)</h4>
              <p>赔率：{PayoutRates[BetType.Banker].description}</p>
              <p>注：庄赢抽取5%佣金</p>
            </div>
            
            <div className="bg-black/20 p-3 rounded-md">
              <h4 className="text-lg font-bold mb-2">闲家 (Player)</h4>
              <p>赔率：{PayoutRates[BetType.Player].description}</p>
            </div>
            
            <div className="bg-black/20 p-3 rounded-md">
              <h4 className="text-lg font-bold mb-2">和局 (Tie)</h4>
              <p>赔率：{PayoutRates[BetType.Tie].description}</p>
            </div>
            
            <div className="bg-black/20 p-3 rounded-md">
              <h4 className="text-lg font-bold mb-2">庄对 / 闲对 (Pairs)</h4>
              <p>赔率：{PayoutRates[BetType.BankerPair].description}</p>
              <p>条件：庄家或闲家的前两张牌是同点数</p>
            </div>
            
            <div className="bg-black/20 p-3 rounded-md">
              <h4 className="text-lg font-bold mb-2">幸运6 (Lucky 6)</h4>
              <p>两张牌：{PayoutRates[BetType.Lucky6].twoCards.description}</p>
              <p>三张牌：{PayoutRates[BetType.Lucky6].threeCards.description}</p>
              <p>条件：庄家得到6点并胜出</p>
            </div>
            
            <div className="bg-black/20 p-3 rounded-md">
              <h4 className="text-lg font-bold mb-2">幸运7 (Lucky 7)</h4>
              <p>普通幸运7（四张牌）：{PayoutRates[BetType.Lucky7].fourCards.description}</p>
              <p>普通幸运7（五张牌）：{PayoutRates[BetType.Lucky7].fiveCards.description}</p>
              <p>超级幸运7（四张牌）：{PayoutRates[BetType.Lucky7].superFourCards.description}</p>
              <p>超级幸运7（五张牌）：{PayoutRates[BetType.Lucky7].superFiveCards.description}</p>
              <p>超级幸运7（六张牌）：{PayoutRates[BetType.Lucky7].superSixCards.description}</p>
            </div>
          </div>
        </div>
        
        <button 
          className="w-full bg-baccarat-gold hover:bg-baccarat-gold/80 text-white py-2 rounded-md font-bold"
          onClick={onClose}
        >
          关闭 (Close)
        </button>
      </div>
    </div>
  );
};

export default HelpModal;
