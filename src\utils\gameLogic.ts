
import { 
  Card, GameResult, BetType, GameHistoryEntry, 
  Bet, PayoutRates, Player
} from "../types/baccarat";
import { 
  calculateHandValue, isPair, shouldPlayerDraw, 
  shouldBankerDraw, shuffleDeck, createMultiDeck
} from "./cardUtils";

// Initialize game with multiple decks
export const initializeGame = (deckCount: number = 8) => {
  const cards = shuffleDeck(createMultiDeck(deckCount));
  
  return {
    cards,
    currentIndex: 0,
    history: [] as GameHistoryEntry[]
  };
};

// Deal initial cards for a new round
export const dealInitialCards = (cards: Card[], currentIndex: number) => {
  const playerCards: Card[] = [
    { ...cards[currentIndex], isRevealed: true },
    { ...cards[currentIndex + 2], isRevealed: true }
  ];
  
  const bankerCards: Card[] = [
    { ...cards[currentIndex + 1], isRevealed: true },
    { ...cards[currentIndex + 3], isRevealed: true }
  ];
  
  return { playerCards, bankerCards, nextIndex: currentIndex + 4 };
};

// Handle the third card draw based on baccarat rules
export const handleThirdCard = (
  playerCards: Card[],
  bankerCards: Card[],
  cards: Card[],
  currentIndex: number
) => {
  let nextIndex = currentIndex;
  let playerThirdCard: Card | undefined;
  
  const playerScore = calculateHandValue(playerCards);
  const bankerScore = calculateHandValue(bankerCards);
  
  // Check if player should draw a third card
  if (shouldPlayerDraw(playerScore)) {
    playerThirdCard = { ...cards[nextIndex], isRevealed: true };
    playerCards.push(playerThirdCard);
    nextIndex++;
  }
  
  // Check if banker should draw a third card
  if (shouldBankerDraw(bankerScore, playerThirdCard)) {
    bankerCards.push({ ...cards[nextIndex], isRevealed: true });
    nextIndex++;
  }
  
  return { playerCards, bankerCards, nextIndex };
};

// Determine the game result
export const determineResult = (
  playerCards: Card[],
  bankerCards: Card[]
): GameResult => {
  const playerScore = calculateHandValue(playerCards);
  const bankerScore = calculateHandValue(bankerCards);
  
  if (playerScore > bankerScore) {
    return GameResult.PlayerWin;
  } else if (bankerScore > playerScore) {
    return GameResult.BankerWin;
  } else {
    return GameResult.Tie;
  }
};

// Check for special conditions (pairs, etc.)
export const checkSpecialConditions = (
  playerCards: Card[],
  bankerCards: Card[]
) => {
  // Check for pairs
  const playerPair = isPair(playerCards[0], playerCards[1]);
  const bankerPair = isPair(bankerCards[0], bankerCards[1]);
  
  // Check for Lucky 6
  const bankerScore = calculateHandValue(bankerCards);
  const lucky6 = bankerScore === 6;
  
  // Check for Lucky 7
  const playerScore = calculateHandValue(playerCards);
  const totalCards = playerCards.length + bankerCards.length;
  const lucky7 = playerScore === 7 && totalCards >= 4;
  
  // Check for Super Lucky 7
  const superLucky7 = playerScore === 7 && bankerScore === 6 && totalCards >= 4;
  
  return { playerPair, bankerPair, lucky6, lucky7, superLucky7 };
};

// Create a game history entry
export const createGameHistoryEntry = (
  playerCards: Card[],
  bankerCards: Card[],
  result: GameResult
): GameHistoryEntry => {
  const playerScore = calculateHandValue(playerCards);
  const bankerScore = calculateHandValue(bankerCards);
  const { playerPair, bankerPair, lucky6, lucky7, superLucky7 } = checkSpecialConditions(playerCards, bankerCards);
  
  return {
    result,
    bankerCards,
    playerCards,
    bankerScore,
    playerScore,
    bankerPair,
    playerPair,
    lucky6,
    lucky7 // This includes both normal and super lucky 7
  };
};

// Calculate payout for bets
export const calculatePayout = (player: Player, historyEntry: GameHistoryEntry): number => {
  let totalPayout = 0;
  const { bets } = player;
  const {
    result,
    bankerCards,
    playerCards,
    bankerPair,
    playerPair,
    lucky6,
    lucky7
  } = historyEntry;
  
  bets.forEach(bet => {
    // Calculate payout based on bet type
    switch (bet.type) {
      case BetType.Banker:
        if (result === GameResult.BankerWin) {
          totalPayout += bet.amount + (bet.amount * PayoutRates[BetType.Banker].rate);
        }
        break;
      
      case BetType.Player:
        if (result === GameResult.PlayerWin) {
          totalPayout += bet.amount + (bet.amount * PayoutRates[BetType.Player].rate);
        }
        break;
      
      case BetType.Tie:
        if (result === GameResult.Tie) {
          totalPayout += bet.amount + (bet.amount * PayoutRates[BetType.Tie].rate);
        }
        break;
      
      case BetType.BankerPair:
        if (bankerPair) {
          totalPayout += bet.amount + (bet.amount * PayoutRates[BetType.BankerPair].rate);
        }
        break;
      
      case BetType.PlayerPair:
        if (playerPair) {
          totalPayout += bet.amount + (bet.amount * PayoutRates[BetType.PlayerPair].rate);
        }
        break;
      
      case BetType.Lucky6:
        if (lucky6) {
          const rate = bankerCards.length === 3
            ? PayoutRates[BetType.Lucky6].threeCards.rate
            : PayoutRates[BetType.Lucky6].twoCards.rate;
          totalPayout += bet.amount + (bet.amount * rate);
        }
        break;
      
      case BetType.Lucky7:
        if (lucky7) {
          const totalCards = playerCards.length + bankerCards.length;
          let rate = 0;
          
          // Check for super lucky 7
          const bankerScore = historyEntry.bankerScore;
          const playerScore = historyEntry.playerScore;
          const isSuperLucky7 = playerScore === 7 && bankerScore === 6;
          
          if (isSuperLucky7) {
            if (totalCards === 4) {
              rate = PayoutRates[BetType.Lucky7].superFourCards.rate;
            } else if (totalCards === 5) {
              rate = PayoutRates[BetType.Lucky7].superFiveCards.rate;
            } else if (totalCards === 6) {
              rate = PayoutRates[BetType.Lucky7].superSixCards.rate;
            }
          } else {
            if (totalCards === 4) {
              rate = PayoutRates[BetType.Lucky7].fourCards.rate;
            } else if (totalCards >= 5) {
              rate = PayoutRates[BetType.Lucky7].fiveCards.rate;
            }
          }
          
          totalPayout += bet.amount + (bet.amount * rate);
        }
        break;
    }
  });
  
  return totalPayout;
};
