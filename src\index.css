@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Playfair Display', serif;
  }
}

/* Baccarat specific styles */
.baccarat-table {
  background-color: theme('colors.baccarat.green');
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  border: 8px solid theme('colors.baccarat.wood');
}

.card {
  @apply w-24 h-36 rounded-lg bg-white relative shadow-lg;
  perspective: 1000px;
}

.card-inner {
  @apply w-full h-full relative transition-transform duration-500 transform-style-preserve-3d;
}

.card-front, .card-back {
  @apply absolute w-full h-full backface-hidden;
}

.card-back {
  @apply bg-blue-900 rounded-lg;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.08'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.card-front {
  @apply bg-white text-black flex items-center justify-center rounded-lg;
  transform: rotateY(180deg);
}

.card-flipped .card-inner {
  transform: rotateY(180deg);
}

.chip {
  @apply w-16 h-16 rounded-full border-4 flex items-center justify-center font-bold text-white shadow-md cursor-pointer;
}

.betting-area {
  @apply border border-baccarat-gold bg-black/20 p-2 rounded-md flex flex-col items-center justify-center;
}

.countdown-timer {
  @apply text-center text-2xl font-bold text-white bg-black bg-opacity-50 rounded-full p-4 mx-auto my-3;
  width: 60px;
  height: 60px;
}

/* Road map (路单) styles */
.road-map-container {
  @apply bg-baccarat-panel bg-opacity-80 p-3 rounded-md;
}

.road-map-cell {
  @apply w-6 h-6 flex items-center justify-center text-xs border border-gray-700;
}

.banker-win {
  @apply bg-baccarat-red rounded-full;
}

.player-win {
  @apply bg-baccarat-blue border-2 rounded-full;
}

.tie-marker {
  @apply border-2 border-baccarat-tie;
}
