
import { GameResult } from "../types/baccarat";

interface ResultModalProps {
  result: GameResult | null;
  bankerScore: number;
  playerScore: number;
  winAmount: number;
  onClose: () => void;
}

const ResultModal: React.FC<ResultModalProps> = ({
  result,
  bankerScore,
  playerScore,
  winAmount,
  onClose,
}) => {
  if (!result) return null;
  
  const resultText = () => {
    switch (result) {
      case GameResult.BankerWin:
        return "庄家赢 (Banker Wins)";
      case GameResult.PlayerWin:
        return "闲家赢 (Player Wins)";
      case GameResult.Tie:
        return "和局 (Tie)";
      default:
        return "";
    }
  };
  
  const resultClass = () => {
    switch (result) {
      case GameResult.BankerWin:
        return "text-baccarat-red";
      case GameResult.PlayerWin:
        return "text-baccarat-blue";
      case GameResult.Tie:
        return "text-baccarat-tie";
      default:
        return "";
    }
  };
  
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-70 z-50">
      <div className="bg-baccarat-panel border-4 border-baccarat-gold rounded-lg p-6 w-80 animate-scale-in">
        <h2 className={`text-2xl font-bold mb-4 ${resultClass()}`}>
          {resultText()}
        </h2>
        
        <div className="flex justify-between mb-4">
          <div className="text-center">
            <div className="text-sm text-white/70">庄家 (Banker)</div>
            <div className="text-3xl text-white">{bankerScore}</div>
          </div>
          
          <div className="text-center">
            <div className="text-sm text-white/70">闲家 (Player)</div>
            <div className="text-3xl text-white">{playerScore}</div>
          </div>
        </div>
        
        {winAmount > 0 && (
          <div className="mb-4 p-3 bg-baccarat-gold/20 rounded-md">
            <div className="text-center text-white">
              <div className="text-sm">您赢得 (You win)</div>
              <div className="text-2xl font-bold">${winAmount}</div>
            </div>
          </div>
        )}
        
        <button 
          className="w-full bg-baccarat-gold hover:bg-baccarat-gold/80 text-white py-2 rounded-md font-bold"
          onClick={onClose}
        >
          继续 (Continue)
        </button>
      </div>
    </div>
  );
};

export default ResultModal;
