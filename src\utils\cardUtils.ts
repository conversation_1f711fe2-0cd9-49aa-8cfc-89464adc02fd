
import { Card, Suit, Value } from "../types/baccarat";

// Create a deck of cards
export const createDeck = (): Card[] => {
  const deck: Card[] = [];
  
  const suits = Object.values(Suit);
  const values = Object.values(Value);
  
  for (const suit of suits) {
    for (const value of values) {
      deck.push({
        suit,
        value,
        isRevealed: false
      });
    }
  }
  
  return deck;
};

// Create multiple decks (usually 8 for baccarat)
export const createMultiDeck = (count: number = 8): Card[] => {
  let multiDeck: Card[] = [];
  
  for (let i = 0; i < count; i++) {
    multiDeck = [...multiDeck, ...createDeck()];
  }
  
  return multiDeck;
};

// Shuffle deck using Fisher-Yates algorithm
export const shuffleDeck = (deck: Card[]): Card[] => {
  const shuffledDeck = [...deck];
  
  for (let i = shuffledDeck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffledDeck[i], shuffledDeck[j]] = [shuffledDeck[j], shuffledDeck[i]];
  }
  
  return shuffledDeck;
};

// Calculate card point value according to baccarat rules
export const getCardPointValue = (card: Card): number => {
  switch (card.value) {
    case Value.Ace:
      return 1;
    case Value.Two:
      return 2;
    case Value.Three:
      return 3;
    case Value.Four:
      return 4;
    case Value.Five:
      return 5;
    case Value.Six:
      return 6;
    case Value.Seven:
      return 7;
    case Value.Eight:
      return 8;
    case Value.Nine:
      return 9;
    default:
      return 0; // 10, J, Q, K all count as 0
  }
};

// Calculate total hand value
export const calculateHandValue = (cards: Card[]): number => {
  const total = cards.reduce((sum, card) => sum + getCardPointValue(card), 0);
  return total % 10; // Only the last digit matters in baccarat
};

// Check if two cards form a pair
export const isPair = (card1: Card, card2: Card): boolean => {
  return card1.value === card2.value;
};

// Determine if a third card should be drawn for player
export const shouldPlayerDraw = (playerScore: number): boolean => {
  return playerScore <= 5;
};

// Determine if a third card should be drawn for banker based on complex rules
export const shouldBankerDraw = (bankerScore: number, playerThirdCard?: Card): boolean => {
  // If banker has 0-2, always draw
  if (bankerScore <= 2) return true;
  
  // If banker has 7-9, never draw
  if (bankerScore >= 7) return false;
  
  // If player didn't draw a third card, banker draws if score is 0-5
  if (!playerThirdCard) return bankerScore <= 5;
  
  // Complex rules based on player's third card
  const playerThirdCardValue = getCardPointValue(playerThirdCard);
  
  switch (bankerScore) {
    case 3:
      return playerThirdCardValue !== 8;
    case 4:
      return [2, 3, 4, 5, 6, 7].includes(playerThirdCardValue);
    case 5:
      return [4, 5, 6, 7].includes(playerThirdCardValue);
    case 6:
      return [6, 7].includes(playerThirdCardValue);
    default:
      return false;
  }
};

// Get card symbol
export const getCardSymbol = (suit: Suit): string => {
  switch (suit) {
    case Suit.Hearts:
      return "♥";
    case Suit.Diamonds:
      return "♦";
    case Suit.Clubs:
      return "♣";
    case Suit.Spades:
      return "♠";
    default:
      return "";
  }
};

// Get card color (red for hearts/diamonds, black for clubs/spades)
export const getCardColor = (suit: Suit): string => {
  return [Suit.Hearts, Suit.Diamonds].includes(suit) ? "text-red-600" : "text-black";
};
