
import { useState } from "react";

interface ChipProps {
  value: number;
  color: string;
  isSelected?: boolean;
  onClick: (value: number) => void;
}

const Chip: React.FC<ChipProps> = ({ value, color, isSelected = false, onClick }) => {
  const [isHovering, setIsHovering] = useState(false);
  
  const handleClick = () => {
    onClick(value);
  };
  
  return (
    <div 
      className={`chip ${color} transform transition-transform duration-200 ${
        isSelected ? 'scale-110 shadow-lg' : ''
      } ${isHovering ? 'scale-105' : ''}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <span className="text-sm font-bold">
        {value >= 1000 ? `${value / 1000}K` : value}
      </span>
    </div>
  );
};

export default Chip;
