
import { GameHistoryEntry, GameResult } from "../types/baccarat";

// Pearl Road (珠盘路) - Basic record of game results
export const generatePearlRoad = (history: GameHistoryEntry[]) => {
  // Organize into a 6-row grid
  const pearlRoad: (GameResult | null)[][] = [];
  let currentColumn = 0;
  
  history.forEach((entry, index) => {
    const row = index % 6;
    
    if (row === 0 && index > 0) {
      currentColumn++;
    }
    
    if (!pearlRoad[currentColumn]) {
      pearlRoad[currentColumn] = Array(6).fill(null);
    }
    
    pearlRoad[currentColumn][row] = entry.result;
  });
  
  return pearlRoad;
};

// Big Road (大路) - Trend visualization with connected wins
export const generateBigRoad = (history: GameHistoryEntry[]) => {
  if (history.length === 0) return [];
  
  const bigRoad: {result: GameResult, ties: number}[][] = [];
  let currentColumn = 0;
  let currentRow = 0;
  let previousResult: GameResult | null = null;
  
  history.forEach((entry) => {
    const { result } = entry;
    
    // For ties, increment tie count on the most recent non-tie result
    if (result === GameResult.Tie) {
      if (bigRoad.length > 0 && bigRoad[currentColumn][currentRow]) {
        bigRoad[currentColumn][currentRow].ties++;
      }
      return;
    }
    
    // If this is the first entry or result changes
    if (previousResult === null || result !== previousResult) {
      currentColumn++;
      currentRow = 0;
      
      if (!bigRoad[currentColumn]) {
        bigRoad[currentColumn] = [];
      }
    } else {
      // Same result as previous, move down
      currentRow++;
      
      // If we've reached 6 rows, move to next column
      if (currentRow >= 6) {
        currentColumn++;
        currentRow = 0;
        
        if (!bigRoad[currentColumn]) {
          bigRoad[currentColumn] = [];
        }
      }
    }
    
    bigRoad[currentColumn][currentRow] = { result, ties: 0 };
    previousResult = result;
  });
  
  return bigRoad;
};

// Small Road (小路) - Pattern detection comparing current and historical positions
export const generateSmallRoad = (bigRoad: {result: GameResult, ties: number}[][]) => {
  if (bigRoad.length < 3) return [];
  
  const smallRoad: ("red" | "blue" | null)[][] = [];
  
  // Start from 3rd column of big road
  for (let col = 2; col < bigRoad.length; col++) {
    const outputCol = col - 2;
    
    if (!smallRoad[outputCol]) {
      smallRoad[outputCol] = [];
    }
    
    // Check each row
    for (let row = 0; row < bigRoad[col].length; row++) {
      // Reference position is two columns back at the same row
      const referenceCol = col - 2;
      
      // Skip if reference position doesn't exist
      if (!bigRoad[referenceCol] || !bigRoad[referenceCol][row]) {
        continue;
      }
      
      const currentResult = bigRoad[col][row].result;
      const referenceResult = bigRoad[referenceCol][row].result;
      
      // If results match, red dot, otherwise blue dot
      smallRoad[outputCol][row] = currentResult === referenceResult ? "red" : "blue";
    }
  }
  
  return smallRoad;
};

// Cockroach Road (小強路/曱甴路) - Diagonal pattern analysis
export const generateCockroachRoad = (bigRoad: {result: GameResult, ties: number}[][]) => {
  if (bigRoad.length < 3) return [];
  
  const cockroachRoad: ("right" | "down" | null)[][] = [];
  
  // Start from 2nd column of big road
  for (let col = 1; col < bigRoad.length; col++) {
    const outputCol = col - 1;
    
    if (!cockroachRoad[outputCol]) {
      cockroachRoad[outputCol] = [];
    }
    
    // Check each row
    for (let row = 0; row < bigRoad[col].length; row++) {
      // Reference position is diagonally (one column back, one row up)
      const referenceCol = col - 1;
      const referenceRow = row - 1;
      
      // Skip if reference position doesn't exist
      if (
        !bigRoad[referenceCol] || 
        referenceRow < 0 || 
        !bigRoad[referenceCol][referenceRow]
      ) {
        continue;
      }
      
      const currentResult = bigRoad[col][row].result;
      const referenceResult = bigRoad[referenceCol][referenceRow].result;
      
      // If results match, right arrow, otherwise down-right arrow
      cockroachRoad[outputCol][row] = currentResult === referenceResult ? "right" : "down";
    }
  }
  
  return cockroachRoad;
};

// Big Eye Boy Road (大眼仔路) - Advanced pattern detection
export const generateBigEyeRoad = (bigRoad: {result: GameResult, ties: number}[][]) => {
  if (bigRoad.length < 2) return [];
  
  const bigEyeRoad: ("red" | "blue" | null)[][] = [];
  
  // Start from 2nd column of big road
  for (let col = 1; col < bigRoad.length; col++) {
    const outputCol = col - 1;
    
    if (!bigEyeRoad[outputCol]) {
      bigEyeRoad[outputCol] = [];
    }
    
    // Check each row
    for (let row = 0; row < bigRoad[col].length; row++) {
      // First check if we can look one column back and one row up
      const hasTopLeft = col > 0 && row > 0 && 
                        bigRoad[col-1] && 
                        bigRoad[col-1][row-1];
      
      // Then check if we can look one column back at the same row
      const hasLeft = col > 0 && 
                    bigRoad[col-1] && 
                    bigRoad[col-1][row];
      
      // Red dot if both exist or both don't exist, blue dot otherwise
      if (hasTopLeft === hasLeft) {
        bigEyeRoad[outputCol][row] = "red";
      } else {
        bigEyeRoad[outputCol][row] = "blue";
      }
    }
  }
  
  return bigEyeRoad;
};

// Calculate statistics from game history
export const calculateStatistics = (history: GameHistoryEntry[]) => {
  let bankerWins = 0;
  let playerWins = 0;
  let ties = 0;
  let bankerPairs = 0;
  let playerPairs = 0;
  let lucky6 = 0;
  let lucky7 = 0;
  
  history.forEach(entry => {
    if (entry.result === GameResult.BankerWin) bankerWins++;
    if (entry.result === GameResult.PlayerWin) playerWins++;
    if (entry.result === GameResult.Tie) ties++;
    if (entry.bankerPair) bankerPairs++;
    if (entry.playerPair) playerPairs++;
    if (entry.lucky6) lucky6++;
    if (entry.lucky7) lucky7++;
  });
  
  return {
    bankerWins,
    playerWins,
    ties,
    bankerPairs,
    playerPairs,
    lucky6,
    lucky7,
    total: history.length
  };
};
