
import React from "react";
import { BetType } from "../types/baccarat";
import { PayoutRates } from "../types/baccarat";

interface BettingAreaProps {
  type: BetType;
  currentBet: number;
  onBetPlaced: (type: BetType) => void;
  disabled: boolean;
}

const BettingArea: React.FC<BettingAreaProps> = ({ 
  type, 
  currentBet, 
  onBetPlaced, 
  disabled 
}) => {
  // Get label and odds based on bet type
  const getBetLabel = () => {
    switch (type) {
      case BetType.Banker:
        return "庄家 (Banker)";
      case BetType.Player:
        return "闲家 (Player)";
      case BetType.Tie:
        return "和局 (Tie)";
      case BetType.BankerPair:
        return "庄对 (Banker Pair)";
      case BetType.PlayerPair:
        return "闲对 (Player Pair)";
      case BetType.Lucky6:
        return "幸运6 (Lucky 6)";
      case BetType.Lucky7:
        return "幸运7 (Lucky 7)";
      default:
        return "";
    }
  };

  const getBetOdds = () => {
    switch (type) {
      case BetType.Banker:
        return PayoutRates[BetType.Banker].description;
      case BetType.Player:
        return PayoutRates[BetType.Player].description;
      case BetType.Tie:
        return PayoutRates[BetType.Tie].description;
      case BetType.BankerPair:
      case BetType.PlayerPair:
        return PayoutRates[BetType.BankerPair].description;
      case BetType.Lucky6:
        return `${PayoutRates[BetType.Lucky6].twoCards.description}/${PayoutRates[BetType.Lucky6].threeCards.description}`;
      case BetType.Lucky7:
        return `${PayoutRates[BetType.Lucky7].fourCards.description}-${PayoutRates[BetType.Lucky7].superSixCards.description}`;
      default:
        return "";
    }
  };

  const getBackgroundClass = () => {
    switch (type) {
      case BetType.Banker:
        return "bg-baccarat-red/20";
      case BetType.Player:
        return "bg-baccarat-blue/20";
      case BetType.Tie:
        return "bg-baccarat-tie/20";
      default:
        return "bg-black/20";
    }
  };

  const handleClick = () => {
    if (!disabled) {
      onBetPlaced(type);
    }
  };

  return (
    <div 
      className={`betting-area ${getBackgroundClass()} cursor-pointer hover:bg-opacity-30 transition-all ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={handleClick}
    >
      <div className="text-sm font-bold text-white">{getBetLabel()}</div>
      <div className="text-xs text-white/70">{getBetOdds()}</div>
      
      {currentBet > 0 && (
        <div className="bg-baccarat-gold text-white text-xs p-1 rounded-full absolute -top-2 -right-2">
          {currentBet}
        </div>
      )}
    </div>
  );
};

export default BettingArea;
