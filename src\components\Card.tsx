
import { Card as CardType, Suit } from "../types/baccarat";
import { getCardSymbol, getCardColor } from "../utils/cardUtils";

interface CardProps {
  card?: CardType;
  isDealing?: boolean;
  dealDelay?: number;
}

const Card: React.FC<CardProps> = ({ card, isDealing = false, dealDelay = 0 }) => {
  if (!card) {
    return <div className="card opacity-0"></div>;
  }

  const cardSymbol = getCardSymbol(card.suit);
  const colorClass = getCardColor(card.suit);

  return (
    <div 
      className={`card ${isDealing ? 'animate-card-deal' : ''} ${card.isRevealed ? 'card-flipped' : ''}`}
      style={{ animationDelay: `${dealDelay}ms` }}
    >
      <div className="card-inner">
        <div className="card-back">
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-16 h-16 rounded-full bg-baccarat-gold flex items-center justify-center text-xl font-bold">B</div>
          </div>
        </div>
        <div className={`card-front ${colorClass}`}>
          <div className="absolute top-2 left-2 flex flex-col items-center">
            <div className="text-xl font-bold">{card.value}</div>
            <div className="text-xl">{cardSymbol}</div>
          </div>
          
          <div className="text-4xl">{cardSymbol}</div>
          
          <div className="absolute bottom-2 right-2 flex flex-col items-center rotate-180">
            <div className="text-xl font-bold">{card.value}</div>
            <div className="text-xl">{cardSymbol}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;
